```{r data generation, echo = FALSE, results = "hide"}
## Base64-encoded PNG clipart images (CC0)
## created by base64enc::base64encode("file.png")
banana    <- "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"
orange    <- "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"
pineapple <- "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"
writeBin(base64enc::base64decode(banana),    "banana.png")
writeBin(base64enc::base64decode(orange),    "orange.png")
writeBin(base64enc::base64decode(pineapple), "pineapple.png")
img0 <- sprintf("![%s](%s.png){width=\"0.85cm\"}", c("banana", "orange", "pineapple"), c("banana", "orange", "pineapple"))
img <- function(x = "b") img0[factor(x, levels = c("b", "o", "p"))]

## prices ("in cent")
b <- sample(20:99, size = 1)
o <- sample(20:99, size = 1)
p <- sample(199:499, size = 1)
x <- c(b, o, p)

## three simple fruit baskets
G <- sample(c("bbo", "bbp", "boo", "bpp", "oop", "opp"), 3)
G <- t(sapply(G, function(x) sample(strsplit(x, "")[[1]])))
A <- t(apply(G, 1, function(x) table(factor(x, levels = c("b", "o", "p")))))

## corresponding prices
d <- A %*% x

## new fruit basket with all fruits and corresponding price
sol <- sum(x)
```


Question
========

Given the following information:

|                  |     |                  |     |                  |   |            |
|:----------------:|:---:|:----------------:|:---:|:----------------:|:-:|-----------:|
| `r img(G[1, 1])` | $+$ | `r img(G[1, 2])` | $+$ | `r img(G[1, 3])` | = | $`r d[1]`$ |
| `r img(G[2, 1])` | $+$ | `r img(G[2, 2])` | $+$ | `r img(G[2, 3])` | = | $`r d[2]`$ |
| `r img(G[3, 1])` | $+$ | `r img(G[3, 2])` | $+$ | `r img(G[3, 3])` | = | $`r d[3]`$ |

Compute:

|              |     |              |     |              |   |            |
|:------------:|:---:|:------------:|:---:|:------------:|:-:|-----------:|
| `r img("b")` | $+$ | `r img("o")` | $+$ | `r img("p")` | = | $\text{?}$ |


Solution
========

The information provided can be interpreted as the price for three fruit baskets
with different combinations of the three fruits. This corresponds to a system of
linear equations where the price of the three fruits is the vector of unknowns $x$:

|         |              |         |              |         |              |
|--------:|:-------------|--------:|:-------------|--------:|:-------------|
| $x_1 =$ | `r img("b")` | $x_2 =$ | `r img("o")` | $x_3 =$ | `r img("p")` |

The system of linear equations is then:
$$
\begin{aligned}
`r toLatex(A, escape = FALSE)` \cdot `r toLatex(matrix(paste0("x_", 1:3), ncol = 1), escape = FALSE)` & = & `r toLatex(matrix(d, ncol = 1), escape = FALSE)`
\end{aligned}
$$
This can be solved using any solution algorithm, e.g., elimination:
$$
x_1 = `r b`, \, x_2 = `r o`, \, x_3 = `r p`.
$$
Based on the three prices for the different fruits it is straightforward to
compute the total price of the fourth fruit basket via:

|              |     |              |     |              |   |            |
|:------------:|:---:|:------------:|:---:|:------------:|:-:|-----------:|
| `r img("b")` | $+$ | `r img("o")` | $+$ | `r img("p")` | = |            |
| $x_1$        | $+$ | $x_2$        | $+$ | $x_3$        | = |            |
| $`r x[1]`$   | $+$ | $`r x[2]`$   | $+$ | $`r x[3]`$   | = | $`r sol`$  |


Meta-information
================
exname: Fruit baskets (numeric)
extype: num
exsolution: `r fmt(sol)`
extol: 0
