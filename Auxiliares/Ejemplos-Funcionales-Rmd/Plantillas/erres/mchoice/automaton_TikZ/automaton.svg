<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="157pt" height="128pt" viewBox="0 0 157 128" version="1.1">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 5.671875 -2.625 L 6.5625 0 L 7.8125 0 L 4.75 -8.71875 L 3.3125 -8.71875 L 0.203125 0 L 1.390625 0 L 2.3125 -2.625 Z M 5.359375 -3.546875 L 2.578125 -3.546875 L 4.015625 -7.515625 Z M 5.359375 -3.546875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 5.234375 -4.515625 C 5.21875 -5.75 4.40625 -6.4375 2.96875 -6.4375 C 1.5 -6.4375 0.5625 -5.6875 0.5625 -4.53125 C 0.5625 -3.546875 1.0625 -3.078125 2.546875 -2.71875 L 3.484375 -2.5 C 4.171875 -2.328125 4.453125 -2.078125 4.453125 -1.625 C 4.453125 -1.046875 3.859375 -0.640625 2.984375 -0.640625 C 2.453125 -0.640625 2 -0.796875 1.75 -1.0625 C 1.59375 -1.25 1.515625 -1.421875 1.453125 -1.859375 L 0.40625 -1.859375 C 0.453125 -0.421875 1.265625 0.28125 2.90625 0.28125 C 4.484375 0.28125 5.484375 -0.5 5.484375 -1.703125 C 5.484375 -2.640625 4.953125 -3.15625 3.71875 -3.453125 L 2.765625 -3.6875 C 1.953125 -3.875 1.609375 -4.140625 1.609375 -4.578125 C 1.609375 -5.15625 2.109375 -5.515625 2.921875 -5.515625 C 3.734375 -5.515625 4.15625 -5.171875 4.1875 -4.515625 Z M 5.234375 -4.515625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 3.03125 -6.265625 L 2.015625 -6.265625 L 2.015625 -7.984375 L 1.015625 -7.984375 L 1.015625 -6.265625 L 0.171875 -6.265625 L 0.171875 -5.453125 L 1.015625 -5.453125 L 1.015625 -0.71875 C 1.015625 -0.078125 1.453125 0.28125 2.21875 0.28125 C 2.46875 0.28125 2.703125 0.25 3.03125 0.1875 L 3.03125 -0.640625 C 2.90625 -0.609375 2.75 -0.59375 2.5625 -0.59375 C 2.125 -0.59375 2.015625 -0.71875 2.015625 -1.15625 L 2.015625 -5.453125 L 3.03125 -5.453125 Z M 3.03125 -6.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d="M 6.390625 -0.578125 C 6.28125 -0.5625 6.234375 -0.5625 6.1875 -0.5625 C 5.828125 -0.5625 5.640625 -0.734375 5.640625 -1.046875 L 5.640625 -4.734375 C 5.640625 -5.84375 4.828125 -6.4375 3.28125 -6.4375 C 2.375 -6.4375 1.625 -6.1875 1.203125 -5.71875 C 0.921875 -5.390625 0.796875 -5.03125 0.78125 -4.40625 L 1.78125 -4.40625 C 1.859375 -5.171875 2.3125 -5.515625 3.25 -5.515625 C 4.140625 -5.515625 4.65625 -5.1875 4.65625 -4.59375 L 4.65625 -4.328125 C 4.65625 -3.90625 4.40625 -3.734375 3.609375 -3.640625 C 2.203125 -3.453125 1.984375 -3.40625 1.609375 -3.25 C 0.875 -2.953125 0.5 -2.390625 0.5 -1.578125 C 0.5 -0.4375 1.296875 0.28125 2.5625 0.28125 C 3.34375 0.28125 3.984375 0 4.6875 -0.640625 C 4.75 -0.015625 5.0625 0.28125 5.71875 0.28125 C 5.921875 0.28125 6.078125 0.25 6.390625 0.171875 Z M 4.65625 -1.96875 C 4.65625 -1.640625 4.546875 -1.4375 4.25 -1.15625 C 3.84375 -0.78125 3.359375 -0.59375 2.765625 -0.59375 C 2 -0.59375 1.546875 -0.96875 1.546875 -1.609375 C 1.546875 -2.265625 1.984375 -2.59375 3.046875 -2.75 C 4.09375 -2.890625 4.3125 -2.9375 4.65625 -3.09375 Z M 4.65625 -1.96875 "/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 0.828125 -6.265625 L 0.828125 0 L 1.828125 0 L 1.828125 -3.25 C 1.828125 -4.140625 2.0625 -4.734375 2.53125 -5.078125 C 2.84375 -5.3125 3.140625 -5.375 3.84375 -5.390625 L 3.84375 -6.40625 C 3.671875 -6.4375 3.578125 -6.4375 3.453125 -6.4375 C 2.8125 -6.4375 2.3125 -6.0625 1.75 -5.125 L 1.75 -6.265625 Z M 0.828125 -6.265625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 0.9375 0 L 4.875 0 C 5.703125 0 6.3125 -0.234375 6.78125 -0.734375 C 7.203125 -1.1875 7.453125 -1.8125 7.453125 -2.484375 C 7.453125 -3.53125 6.96875 -4.171875 5.859375 -4.609375 C 6.640625 -4.96875 7.0625 -5.625 7.0625 -6.5 C 7.0625 -7.140625 6.828125 -7.703125 6.375 -8.109375 C 5.921875 -8.515625 5.3125 -8.71875 4.484375 -8.71875 L 0.9375 -8.71875 Z M 2.0625 -4.953125 L 2.0625 -7.734375 L 4.203125 -7.734375 C 4.828125 -7.734375 5.171875 -7.65625 5.46875 -7.421875 C 5.78125 -7.1875 5.953125 -6.828125 5.953125 -6.34375 C 5.953125 -5.875 5.78125 -5.515625 5.46875 -5.265625 C 5.171875 -5.046875 4.828125 -4.953125 4.203125 -4.953125 Z M 2.0625 -0.984375 L 2.0625 -3.984375 L 4.765625 -3.984375 C 5.3125 -3.984375 5.671875 -3.84375 5.921875 -3.5625 C 6.1875 -3.28125 6.328125 -2.90625 6.328125 -2.46875 C 6.328125 -2.0625 6.1875 -1.671875 5.921875 -1.40625 C 5.671875 -1.109375 5.3125 -0.984375 4.765625 -0.984375 Z M 2.0625 -0.984375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-7">
<path style="stroke:none;" d="M 7.90625 -6.015625 C 7.5625 -7.921875 6.46875 -8.859375 4.546875 -8.859375 C 3.375 -8.859375 2.4375 -8.484375 1.796875 -7.765625 C 1 -6.90625 0.578125 -5.671875 0.578125 -4.25 C 0.578125 -2.828125 1.015625 -1.59375 1.84375 -0.734375 C 2.515625 -0.046875 3.375 0.28125 4.5 0.28125 C 6.640625 0.28125 7.828125 -0.875 8.09375 -3.171875 L 6.9375 -3.171875 C 6.84375 -2.578125 6.734375 -2.171875 6.546875 -1.828125 C 6.1875 -1.109375 5.453125 -0.703125 4.515625 -0.703125 C 2.78125 -0.703125 1.6875 -2.09375 1.6875 -4.265625 C 1.6875 -6.5 2.734375 -7.875 4.421875 -7.875 C 5.125 -7.875 5.78125 -7.65625 6.140625 -7.328125 C 6.46875 -7.03125 6.640625 -6.65625 6.78125 -6.015625 Z M 7.90625 -6.015625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-8">
<path style="stroke:none;" d="M 1.0625 0 L 4.421875 0 C 6.625 0 7.96875 -1.65625 7.96875 -4.359375 C 7.96875 -7.0625 6.640625 -8.71875 4.421875 -8.71875 L 1.0625 -8.71875 Z M 2.171875 -0.984375 L 2.171875 -7.734375 L 4.234375 -7.734375 C 5.953125 -7.734375 6.859375 -6.578125 6.859375 -4.34375 C 6.859375 -2.15625 5.953125 -0.984375 4.234375 -0.984375 Z M 2.171875 -0.984375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-9">
<path style="stroke:none;" d="M 3.09375 -6.03125 L 3.09375 0 L 4.140625 0 L 4.140625 -8.46875 L 3.453125 -8.46875 C 3.078125 -7.171875 2.84375 -7 1.21875 -6.796875 L 1.21875 -6.03125 Z M 3.09375 -6.03125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-10">
<path style="stroke:none;" d="M 3.28125 -8.46875 C 2.5 -8.46875 1.78125 -8.125 1.34375 -7.546875 C 0.78125 -6.796875 0.515625 -5.671875 0.515625 -4.09375 C 0.515625 -1.25 1.46875 0.28125 3.28125 0.28125 C 5.078125 0.28125 6.0625 -1.25 6.0625 -4.03125 C 6.0625 -5.671875 5.796875 -6.78125 5.234375 -7.546875 C 4.796875 -8.140625 4.09375 -8.46875 3.28125 -8.46875 Z M 3.28125 -7.546875 C 4.421875 -7.546875 4.984375 -6.390625 4.984375 -4.125 C 4.984375 -1.71875 4.4375 -0.59375 3.265625 -0.59375 C 2.15625 -0.59375 1.59375 -1.765625 1.59375 -4.09375 C 1.59375 -6.40625 2.15625 -7.546875 3.28125 -7.546875 Z M 3.28125 -7.546875 "/>
</symbol>
</g>
</defs>
<g id="surface1">
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 12.453594 -0.000875 C 12.453594 6.878031 6.879375 12.45225 0.00046875 12.45225 C -6.878437 12.45225 -12.452656 6.878031 -12.452656 -0.000875 C -12.452656 -6.879781 -6.878437 -12.454 0.00046875 -12.454 C 6.879375 -12.454 12.453594 -6.879781 12.453594 -0.000875 Z M 12.453594 -0.000875 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="51.868" y="27.305"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -28.284687 -0.000875 L -14.108906 -0.000875 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<path style="fill:none;stroke-width:0.31879;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.196969 1.592875 C -1.095406 0.995219 -0.00165625 0.100687 0.299125 -0.000875 C -0.00165625 -0.0985313 -1.095406 -0.996969 -1.196969 -1.594625 " transform="matrix(1,0,0,-1,41.74775,23.046)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-2" x="0.321" y="27.033"/>
  <use xlink:href="#glyph0-3" x="6.2986" y="27.033"/>
  <use xlink:href="#glyph0-4" x="9.622146" y="27.033"/>
  <use xlink:href="#glyph0-5" x="16.269237" y="27.033"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-3" x="20.728526" y="27.033"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 12.453594 -82.000875 C 12.453594 -75.121969 6.879375 -69.543844 0.00046875 -69.543844 C -6.878437 -69.543844 -12.452656 -75.121969 -12.452656 -82.000875 C -12.452656 -88.875875 -6.878437 -94.454 0.00046875 -94.454 C 6.879375 -94.454 12.453594 -88.875875 12.453594 -82.000875 Z M 12.453594 -82.000875 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="51.868" y="109.303"/>
</g>
<path style="fill:none;stroke-width:1.39478;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 94.750469 -0.000875 C 94.750469 6.878031 89.17625 12.45225 82.297344 12.45225 C 75.418438 12.45225 69.844219 6.878031 69.844219 -0.000875 C 69.844219 -6.879781 75.418438 -12.454 82.297344 -12.454 C 89.17625 -12.454 94.750469 -6.879781 94.750469 -0.000875 Z M 94.750469 -0.000875 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<path style="fill:none;stroke-width:0.59776;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(100%,100%,100%);stroke-opacity:1;stroke-miterlimit:10;" d="M 94.750469 -0.000875 C 94.750469 6.878031 89.17625 12.45225 82.297344 12.45225 C 75.418438 12.45225 69.844219 6.878031 69.844219 -0.000875 C 69.844219 -6.879781 75.418438 -12.454 82.297344 -12.454 C 89.17625 -12.454 94.750469 -6.879781 94.750469 -0.000875 Z M 94.750469 -0.000875 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-7" x="133.836" y="27.32"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 94.750469 -82.29775 C 94.750469 -75.418844 89.17625 -69.844625 82.297344 -69.844625 C 75.418438 -69.844625 69.844219 -75.418844 69.844219 -82.29775 C 69.844219 -89.176656 75.418438 -94.750875 82.297344 -94.750875 C 89.17625 -94.750875 94.750469 -89.176656 94.750469 -82.29775 Z M 94.750469 -82.29775 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-8" x="133.836" y="109.601"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 4.328594 -11.887594 C 12.094219 -33.223531 12.094219 -48.774313 4.824688 -68.743063 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<path style="fill:none;stroke-width:0.31879;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.194882 1.593591 C -1.094588 0.998231 0.00133597 0.100124 0.299393 0.000756113 C -0.00060847 -0.10012 -1.095679 -0.997526 -1.194292 -1.594613 " transform="matrix(-0.34201,0.93968,0.93968,0.34201,60.67981,91.78872)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="69.526" y="68.304"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 11.891094 4.32725 C 33.30125 12.163187 48.687969 12.190531 68.758281 4.924906 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<path style="fill:none;stroke-width:0.31879;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.194093 1.595347 C -1.097172 0.994804 -0.000447691 0.0997957 0.297441 -0.0000887358 C 0.000934491 -0.0991169 -1.095699 -0.994637 -1.194017 -1.595232 " transform="matrix(0.94026,0.34038,0.34038,-0.94026,124.61489,18.12133)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-10" x="93.529" y="9.054"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -4.327656 -70.11025 C -12.093281 -48.774313 -12.093281 -33.223531 -4.82375 -13.254781 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<path style="fill:none;stroke-width:0.31879;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.193428 1.593123 C -1.096805 0.996426 -0.000880604 0.0983195 0.297177 -0.00104832 C 0.000845698 -0.100588 -1.09556 -0.994324 -1.196509 -1.596418 " transform="matrix(0.34201,-0.93968,-0.93968,-0.34201,51.03019,36.30233)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="35.537" y="68.304"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 11.906719 -77.715719 C 33.461406 -69.957906 48.92625 -70.012594 69.059063 -77.426656 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<path style="fill:none;stroke-width:0.31879;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.195077 1.59402 C -1.096295 0.997666 0.000573123 0.0984972 0.298993 0.000206227 C -0.000647115 -0.101756 -1.095183 -0.995801 -1.195304 -1.594601 " transform="matrix(0.93842,0.3454,0.3454,-0.93842,124.91466,100.47114)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-10" x="93.717" y="91.224"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 86.727031 -12.168844 C 94.48875 -33.606344 94.461406 -48.996969 87.125469 -69.043844 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<path style="fill:none;stroke-width:0.31879;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.196881 1.592335 C -1.095555 0.997139 -0.00172745 0.0995721 0.300175 0.00206103 C 0.00034319 -0.0993359 -1.094534 -0.994984 -1.195784 -1.593594 " transform="matrix(-0.34363,0.93907,0.93907,0.34363,142.98012,92.0885)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="151.898" y="68.604"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70.129375 -4.430563 C 48.687969 -12.188375 33.30125 -12.161031 13.254375 -4.829 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<path style="fill:none;stroke-width:0.31879;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.193997 1.594979 C -1.094996 0.994772 -0.00116871 0.0972048 0.300734 -0.000306203 C -0.000440457 -0.0980346 -1.093975 -0.997351 -1.195225 -1.595961 " transform="matrix(-0.93907,-0.34363,-0.34363,0.93907,69.11043,27.87316)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-10" x="93.529" y="45.31"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 77.969219 -70.407125 C 70.137188 -48.996969 70.105938 -33.606344 77.371563 -13.539938 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<path style="fill:none;stroke-width:0.31879;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.193532 1.59295 C -1.094267 0.99741 -0.00121661 0.101071 0.296672 0.001187 C 0.00016557 -0.0978412 -1.095138 -0.997035 -1.194786 -1.593957 " transform="matrix(0.34038,-0.94026,-0.94026,-0.34038,133.22826,36.58404)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-9" x="117.758" y="68.604"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 70.395 -86.582906 C 48.836406 -94.340719 33.371563 -94.282125 13.23875 -86.871969 " transform="matrix(1,0,0,-1,55.855,23.046)"/>
<path style="fill:none;stroke-width:0.31879;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.195528 1.595586 C -1.095397 0.995566 0.000122082 0.100063 0.298542 0.00177185 C -0.00109816 -0.10019 -1.095634 -0.994236 -1.195755 -1.593035 " transform="matrix(-0.93842,-0.3454,-0.3454,0.93842,69.09327,109.91786)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-10" x="93.643" y="127.435"/>
</g>
</g>
</svg>
