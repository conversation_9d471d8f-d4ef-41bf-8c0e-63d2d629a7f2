---
output:
  html_document: default
  pdf_document: default
---
```{r, echo = FALSE, results = "hide"}
library(exams)
library(knitr)
library(tikzDevice)

# Usa el mismo tipo de gráfico (pdf, svg, png) que la llamada actual de xweave()
typ <- match_exams_device()

# Datos para la tabla
datos_tabla <- data.frame(
  Dia = c("Lunes", "Martes", "Miércoles", "Jueves", "Viernes"),
  Fila1 = c("A", "B", "C", "D", "E"),
  Fila2 = c("F", "G", "H", "J", "K"),
  Fila3 = c("A", "B", "C", "D", "E"),
  Fila4 = c("F", "G", "H", "J", "K")
)

tikz_tabla <- function(datos) {
  num_filas <- nrow(datos)
  num_cols <- ncol(datos)

  tikz_code <- c(
    "\\begin{tikzpicture}",
    "\\matrix (first) [table,text width=6em] {")

  # Encabezado
  tikz_code <- c(tikz_code, paste0("&", paste(names(datos), collapse=" & "), "\\\\"))

  # Filas de datos
  for (i in 1:num_filas) {
    fila_str <- paste0(i, " & ")
    for (j in 1:num_cols) {
      fila_str <- paste0(fila_str, datos[i, j], " & ")
    }
    fila_str <- substr(fila_str, 1, nchar(fila_str) - 2) # Eliminar el último " & "
    fila_str <- paste0(fila_str, "\\\\")
    tikz_code <- c(tikz_code, fila_str)
  }

  tikz_code <- c(tikz_code, "};", "\\end{tikzpicture}")
  
  return(paste(tikz_code, collapse="\n"))
}

tikz_preambulo <- c(
  "\\documentclass[tikz,border=10pt]{standalone}",
  "\\usetikzlibrary{matrix}",
  "\\begin{document}",

  "\\tikzset{", 
    "table/.style={",
        "matrix,",
        "row sep=-\\pgflinewidth,",
        "column sep=-\\pgflinewidth,",
        "nodes={",
            "rectangle,",
            "draw=black,",
            "align=center",
        "},",
        "minimum height=1.5em,",
        "text depth=0.5ex,",
        "text height=2ex,",
        "every even row/.style={",
            "nodes={fill=gray!20}",
        "},",
        "column 1/.style={",
            "nodes={text width=2em,font=\\bfseries}",
        "},",
        "row 1/.style={",
            "nodes={",
                "fill=black,",
                "text=white,",
                "font=\\bfseries",
            "}",
        "}",
    "}",
  "}",
  "" # Linea en blanco importante para evitar problemas con tikzDevice
  )

# Combine preamble and table code
tikz_completo <- c(tikz_preambulo, tikz_tabla(datos_tabla), "\\end{document}")
```

```{r, echo = FALSE, results = "asis"}
include_tikz(tikz_completo,
             name = "tabla_semanal", 
             markup = "markdown",
             format = typ,
             packages = c("tikz"),
             width = "12cm")
```