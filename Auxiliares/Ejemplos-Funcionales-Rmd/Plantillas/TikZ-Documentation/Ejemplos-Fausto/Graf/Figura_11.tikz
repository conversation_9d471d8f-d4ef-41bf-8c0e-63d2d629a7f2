﻿\usepackage{tikz-cd}
\usetikzlibrary{3d, angles,calc}

\begin{tikzpicture}[x={5mm}, y={5mm}, z={5mm}, >=latex]
    \draw[dashed] (0, 0, 0) coordinate (E) -- (0, 0, 8) -- (18, 0, 8);
    \draw[dashed] (0, 0, 8) -- (0, 6, 8) coordinate(D);
    \draw[fill=green, opacity=.5] (E) -- (18, 0, 0) coordinate (A) -- (18, 6, 0) -- (0, 6, 0) coordinate (B) -- cycle;
    \draw[fill=green, opacity=.5] (A) -- (18, 0, 8) -- (18, 6, 8) coordinate (C) -- (18, 6, 0) -- cycle;
    \draw[fill=green, opacity=.25] (B) -- (18, 6, 0) -- (C) -- (D) -- cycle;
    \draw[<->] (0, -.5, 0) -- (18, -.5, 0) node[midway, fill=white] {18 cm};
    \draw pic["$23.96^o$", angle eccentricity=1.3, draw, angle radius=2cm, red] {angle = D--C--B};
    \draw[red] (C) -- (B);
    \draw pic["$18.43^o$", angle eccentricity=1.3, draw, angle radius=2cm, red] {angle = B--A--E};
    \draw[red] (A) -- (B);
\end{tikzpicture}