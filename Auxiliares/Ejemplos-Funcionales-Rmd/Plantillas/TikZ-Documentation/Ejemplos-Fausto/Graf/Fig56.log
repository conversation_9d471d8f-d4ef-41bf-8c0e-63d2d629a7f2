This is LuaHBTeX, Version 1.12.0 (TeX Live 2020)  (format=lualatex 2021.6.1)  7 JUL 2021 09:09
 system commands enabled.
**Fig56.tex
(./Fig56.tex
LaTeX2e <2020-02-02> patch level 5
Lua module: luaotfload-main 2020-05-06 3.14 luaotfload entry point
Lua module: luaotfload-init 2020-05-06 3.14 luaotfload submodule / initializatio
n
Lua module: lualibs 2020-02-02 2.70 ConTeXt Lua standard libraries.
Lua module: lualibs-extended 2020-02-02 2.70 ConTeXt Lua libraries -- extended c
ollection.
Lua module: luaotfload-log 2020-05-06 3.14 luaotfload submodule / logging
Lua module: luaotfload-parsers 2020-05-06 3.14 luaotfload submodule / filelist
Lua module: luaotfload-configuration 2020-05-06 3.14 luaotfload submodule / conf
ig file reader
luaotfload | conf : Root cache directory is "/home/<USER>/.texlive2020/texmf-va
r/luatex-cache/generic-dev/names".
luaotfload | init : Loading fontloader "fontloader-2020-05-06.lua" from kpse-res
olved path "/usr/share/texlive/texmf-dist/tex/luatex/luaotfload/fontloader-2020-
05-06.lua".
Lua-only attribute luaotfload@noligature = 2
Lua-only attribute luaotfload@syllabe = 3
luaotfload | init : Context OpenType loader version 0x1.8e353f7ced917p+1
Lua module: luaotfload-fallback 2020-05-06 3.14 luaotfload submodule / fallback
Lua module: luaotfload-multiscript 2020-05-06 3.14 luaotfload submodule / multis
cript
Lua module: luaotfload-script 2020-05-06 3.14 luaotfload submodule / Script help
ers
Inserting `luaotfload.node_processor' at position 1 in `pre_linebreak_filter'.
Inserting `luaotfload.node_processor' at position 1 in `hpack_filter'.
Lua module: luaotfload-loaders 2020-05-06 3.14 luaotfload submodule / callback h
andling
Inserting `luaotfload.define_font' at position 1 in `define_font'.
Lua module: luaotfload-database 2020-05-06 3.14 luaotfload submodule / database
Lua module: luaotfload-unicode 2020-05-06 3.14 luaotfload submodule / Unicode he
lpers
Lua module: luaotfload-colors 2020-05-06 3.14 luaotfload submodule / color
Lua-only attribute luaotfload_color_attribute = 4
Lua module: luaotfload-resolvers 2020-05-06 3.14 luaotfload submodule / resolver
s
luaotfload | conf : Root cache directory is "/home/<USER>/.texlive2020/texmf-va
r/luatex-cache/generic-dev/names".
Lua module: luaotfload-features 2020-05-06 3.14 luaotfload submodule / features
Lua module: luaotfload-harf-define 2020-05-06 3.14 luaotfload submodule / databa
se
Inserting `luaotfload.harf.strip_prefix' at position 1 in `find_opentype_file'.
Inserting `luaotfload.harf.strip_prefix' at position 1 in `find_truetype_file'.
Lua module: luaotfload-harf-plug 2020-05-06 3.14 luaotfload submodule / database
Inserting `Harf pre_output_filter callback' at position 1 in `pre_output_filter'
.
Inserting `Harf wrapup_run callback' at position 1 in `wrapup_run'.
Inserting `Harf finish_pdffile callback' at position 1 in `finish_pdffile'.
Inserting `Harf glyph_info callback' at position 1 in `glyph_info'.
Lua module: luaotfload-letterspace 2020-05-06 3.14 luaotfload submodule / color
Lua module: luaotfload-embolden 2020-05-06 3.14 luaotfload submodule / color
Lua module: luaotfload-notdef 2020-05-06 3.14 luaotfload submodule / color
Lua module: luaotfload-suppress 2020-05-06 3.14 luaotfload submodule / suppress
Lua module: luaotfload-szss 2020-05-06 3.14 luaotfload submodule / color
Lua module: luaotfload-auxiliary 2020-05-06 3.14 luaotfload submodule / auxiliar
y functions
Inserting `luaotfload.aux.set_sscale_dimens' at position 1 in `luaotfload.patch_
font'.
Inserting `luaotfload.aux.set_font_index' at position 2 in `luaotfload.patch_fon
t'.
Inserting `luaotfload.aux.patch_cambria_domh' at position 3 in `luaotfload.patch
_font'.
Inserting `luaotfload.aux.fixup_fontdata' at position 1 in `luaotfload.patch_fon
t_unsafe'.
Inserting `luaotfload.aux.set_capheight' at position 4 in `luaotfload.patch_font
'.
Inserting `luaotfload.aux.set_xheight' at position 5 in `luaotfload.patch_font'.
Lua module: luaotfload-tounicode 2020-05-06 3.14 luaotfload submodule / tounicod
e
Inserting `luaotfload.rewrite_fontname' at position 6 in `luaotfload.patch_font'
. L3 programming layer <2020-04-06>
(/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2019/12/20 v1.4l Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2019/12/20 v1.4l Standard LaTeX file (size option)
luaotfload | db : Font names database loaded from /home/<USER>/.texlive2020/tex
mf-var/luatex-cache/generic-dev/names/luaotfload-names.luc)
\c@part=\count163
\c@section=\count164
\c@subsection=\count165
\c@subsubsection=\count166
\c@paragraph=\count167
\c@subparagraph=\count168
\c@figure=\count169
\c@table=\count170
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen134
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks15
\pgfutil@tempdima=\dimen135
\pgfutil@tempdimb=\dimen136

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common-lists.t
ex)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box45
(/usr/share/texlive/texmf-dist/tex/latex/ms/everyshi.sty
Package: everyshi 2001/05/15 v3.00 EveryShipout Package (MS)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2020/01/08 v3.1.5b (3.1.5b)
))
Package: pgf 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2019/11/30 v1.2a Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks16
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2019/11/30 v1.4a Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2016/01/03 v1.10 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: luatex.def on input line 105.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/luatex.def
File: luatex.def 2018/01/08 v1.0l Graphics/color driver for luatex
))
\Gin@req@height=\dimen137
\Gin@req@width=\dimen138
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks17
\pgfkeys@temptoks=\toks18

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeysfiltered.code.t
ex
\pgfkeys@tmptoks=\toks19
))
\pgf@x=\dimen139
\pgf@y=\dimen140
\pgf@xa=\dimen141
\pgf@ya=\dimen142
\pgf@xb=\dimen143
\pgf@yb=\dimen144
\pgf@xc=\dimen145
\pgf@yc=\dimen146
\pgf@xd=\dimen147
\pgf@yd=\dimen148
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count171
\c@pgf@countb=\count172
\c@pgf@countc=\count173
\c@pgf@countd=\count174
\t@pgf@toka=\toks20
\t@pgf@tokb=\toks21
\t@pgf@tokc=\toks22
\pgf@sys@id@count=\count175
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2020/01/08 v3.1.5b (3.1.5b)
)
Driver file for pgf: pgfsys-luatex.def

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-luatex.def
File: pgfsys-luatex.def 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.de
f
File: pgfsys-common-pdf.def 2020/01/08 v3.1.5b (3.1.5b)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.
tex
File: pgfsyssoftpath.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfsyssoftpath@smallbuffer@items=\count176
\pgfsyssoftpath@bigbuffer@items=\count177
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.
tex
File: pgfsysprotocol.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)) (/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2016/05/11 v2.12 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: luatex.def on input line 225.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1348.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1352.
Package xcolor Info: Model `RGB' extended on input line 1364.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1366.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1367.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1370.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1371.
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen149
\pgfmath@count=\count178
\pgfmath@box=\box46
\pgfmath@toks=\toks23
\pgfmath@stack@operand=\toks24
\pgfmath@stack@operation=\toks25
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonomet
ric.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.cod
e.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison
.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerari
thmetics.code.tex)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count179
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.te
x
File: pgfcorepoints.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@picminx=\dimen150
\pgf@picmaxx=\dimen151
\pgf@picminy=\dimen152
\pgf@picmaxy=\dimen153
\pgf@pathminx=\dimen154
\pgf@pathmaxx=\dimen155
\pgf@pathminy=\dimen156
\pgf@pathmaxy=\dimen157
\pgf@xx=\dimen158
\pgf@xy=\dimen159
\pgf@yx=\dimen160
\pgf@yy=\dimen161
\pgf@zx=\dimen162
\pgf@zy=\dimen163
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.
code.tex
File: pgfcorepathconstruct.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@path@lastx=\dimen164
\pgf@path@lasty=\dimen165
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code
.tex
File: pgfcorepathusage.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@shorten@end@additional=\dimen166
\pgf@shorten@start@additional=\dimen167
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.te
x
File: pgfcorescopes.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfpic=\box47
\pgf@hbox=\box48
\pgf@layerbox@main=\box49
\pgf@picture@serial@count=\count180
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.c
ode.tex
File: pgfcoregraphicstate.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgflinewidth=\dimen168
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformation
s.code.tex
File: pgfcoretransformations.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@pt@x=\dimen169
\pgf@pt@y=\dimen170
\pgf@pt@temp=\dimen171
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.t
ex
File: pgfcoreobjects.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing
.code.tex
File: pgfcorepathprocessing.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.te
x
File: pgfcorearrows.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfarrowsep=\dimen172
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@max=\dimen173
\pgf@sys@shading@range@num=\count181
\pgf@shadingcount=\count182
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.
tex
File: pgfcoreexternal.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfexternal@startupbox=\box50
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.te
x
File: pgfcorelayers.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.c
ode.tex
File: pgfcoretransparency.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.
tex
File: pgfcorepatterns.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfnodeparttextbox=\box51
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65
.sty
Package: pgfcomp-version-0-65 2020/01/08 v3.1.5b (3.1.5b)
\pgf@nodesepstart=\dimen174
\pgf@nodesepend=\dimen175
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18
.sty
Package: pgfcomp-version-1-18 2020/01/08 v3.1.5b (3.1.5b)
)) (/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texlive/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
\pgffor@iter=\dimen176
\pgffor@skip=\dimen177
\pgffor@stack=\toks26
\pgffor@toks=\toks27
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers
.code.tex
File: pgflibraryplothandlers.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@plot@mark@count=\count183
\pgfplotmarksize=\dimen178
)
\tikz@lastx=\dimen179
\tikz@lasty=\dimen180
\tikz@lastxsaved=\dimen181
\tikz@lastysaved=\dimen182
\tikz@lastmovetox=\dimen183
\tikz@lastmovetoy=\dimen184
\tikzleveldistance=\dimen185
\tikzsiblingdistance=\dimen186
\tikz@figbox=\box52
\tikz@figbox@bg=\box53
\tikz@tempbox=\box54
\tikz@tempbox@bg=\box55
\tikztreelevel=\count184
\tikznumberofchildren=\count185
\tikznumberofcurrentchild=\count186
\tikz@fig@count=\count187

(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfmatrixcurrentrow=\count188
\pgfmatrixcurrentcolumn=\count189
\pgf@matrix@numberofcolumns=\count190
)
\tikz@expandcount=\count191

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)))
(/usr/share/texlive/texmf-dist/tex/latex/tikz-3dplot/tikz-3dplot.sty
(/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2014/09/29 v1.1c Standard LaTeX ifthen package (DPC)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarycalc.code.tex
File: tikzlibrarycalc.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrary3d.code.tex
File: tikzlibrary3d.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryarrows.code.tex
File: tikzlibraryarrows.code.tex 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.
tex
File: pgflibraryarrows.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\arrowsize=\dimen187
))) (/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdfmode.def
File: l3backend-pdfmode.def 2020-03-12 L3 backend support: PDF mode
\l__kernel_color_stack_int=\count192
\l__pdf_internal_box=\box56
) (./Fig56.aux)
\openout1 = Fig56.aux

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 6.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 6.

 (/usr/share/texlive/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2019/12/16 v2.5j Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.

ABD: EveryShipout initializing macros
(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count193
\scratchdimen=\dimen188
\scratchbox=\box57
\nofMPsegments=\count194
\nofMParguments=\count195
\everyMPshowfont=\toks28
\MPscratchCnt=\count196
\MPscratchDim=\dimen189
\MPnumerator=\count197
\makeMPintoPDFobject=\count198
\everyMPtoPDFconversion=\toks29
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 48
5.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live

))
[1

Missing character: There is no ; (U+003B) in font nullfont!
Missing character: There is no ; (U+003B) in font nullfont!
{/usr/share/texlive/texmf-dist/fonts/map/pdftex/updmap/pdftex.map}]
(./Fig56.aux))

Here is how much of LuaTeX's memory you used:
 12748 strings out of 480991
 100000,552014 words of node,token memory allocated
 413 words of node memory still in use:
   3 hlist, 1 vlist, 1 rule, 2 glue, 3 kern, 1 glyph, 5 attribute, 48 glue_spec,
 5 attribute_list, 2 write nodes
   avail lists: 2:973,3:261,4:5,5:22,6:1,7:261,9:113
 29821 multiletter control sequences out of 65536+600000
 26 fonts using 3390395 bytes
 60i,6n,60p,409b,787s stack positions out of 5000i,500n,10000p,200000b,100000s
</usr/share/texlive/texmf-dist/fonts/opentype/public/lm/lmroman10-regular.otf></
usr/share/texlive/texmf-dist/fonts/opentype/public/lm/lmroman9-regular.otf>
Output written on Fig56.pdf (1 page, 5017 bytes).

PDF statistics: 25 PDF objects out of 1000 (max. 8388607)
 15 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 131072)
 16 words of extra memory for PDF output out of 10000 (max. 100000000)

