% Pgfplots demo
% Author: <PERSON>
% Source: Pgfplots manual:
% http://www.ctan.org/tex-archive/help/Catalogue/entries/pgfplots.html
\documentclass{article}

\usepackage{tikz}
\usepackage{tkz-euclide}
\usepackage{pgfplots}
\usepackage{verbatim}

\begin{document}
\begin{tikzpicture}[scale=1.25]
\tkzDefPoint(0,0){A}
\tkzDefPoint(4.875,0){B}
\tkzDefPoint(1.5,2.25){C}
\tkzDefPoint(1.5,0){D}
\tkzDrawPolygon(A,B,C)
\tkzMarkRightAngle[fill=orange!40](A,C,B)
\tkzMarkRightAngle[fill=orange!40](C,D,B)
\tkzSetUpLine[line width=0.5pt,color=blue]
\tkzLabelSegment[swap](D,C){$x$}
%\tkzLabelSegment[swap](C,D){$x$}
\tkzDrawLine[altitude](A,C,B)
    \draw[|-|] (1.5, -0.1) -- (4.875, -0.1) node[midway, below] {\ 9 cm};
    \draw[|-|] (0, -0.1) -- (1.5, -0.1) node[midway, below] {\ 4 cm};
%    \draw(C) -- (D) node[midway, right] {$x$};
\end{tikzpicture}
\end{document}