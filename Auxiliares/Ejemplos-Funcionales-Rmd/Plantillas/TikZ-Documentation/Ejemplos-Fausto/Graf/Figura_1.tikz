﻿\usetikzlibrary{3d, calc, babel}

\begin{tikzpicture}[x={5mm}, y={5mm}, z={(90:5mm)}, >=latex]
    \fill[orange!75, draw=black] (0, 0, 0) node (O) {} -- (4, 0, 0) node (A) {} -- (2, {sqrt(5)}, 0) node (B) {} -- cycle;
    \fill[green!25, draw=black] (0, 0, 0) -- (2, {sqrt(5)}, 0) -- (2, {sqrt(5)}, 8) -- (0, 0, 8) -- cycle;
    \fill[green!75!yellow, draw=black] (2, {sqrt(5)}, 0) -- (4, 0, 0) -- (4, 0, 8) node (C) {} -- (2, {sqrt(5)}, 8) -- cycle;
    \draw[|-|] ($(O) + (-.25, 0, .25)$) -- ($(B) + (-.25, 0, .25)$) node[midway, left] {\tiny 3 cm};
    \draw[|-|] ($(O) + (0, -.25, 0)$) -- ($(A) + (0, -.25, 0)$) node[midway, below] {\tiny 4 cm};
    \draw[|-|] ($(A) + (.25, 0, 0)$) -- ($(C) + (.25, 0, 0)$) node[midway, right] {\tiny 8 cm};
    \draw[dashed] (2, 0, 0) -- (B);
\end{tikzpicture}