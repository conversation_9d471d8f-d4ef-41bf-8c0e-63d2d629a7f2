% Template TikZ para Gráfico Circular - Alineado ICFES
% Competencia: Pensamiento Aleatorio - Interpretación y Representación
% Nivel: 1-2 (Lectura e interpretación de gráficos)

\begin{tikzpicture}[scale=`r escala_grafico`]

% Variables parametrizables desde R
% En el chunk R definir:
% porcentajes <- c(30, 25, 20, 15, 10)  # Deben sumar 100
% etiquetas <- c("Categoría A", "Categoría B", "Categoría C", "Categoría D", "Categoría E")
% colores <- c("red!70", "blue!70", "green!70", "yellow!70", "purple!70")
% escala_grafico <- 1.2

% Centro del círculo
\coordinate (centro) at (0,0);

% Radio del gráfico circular
\pgfmathsetmacro{\radio}{2}

% Calcular ángulos acumulativos
% Primer sector: 0° a `r porcentajes[1] * 3.6`°
\pgfmathsetmacro{\anguloA}{0}
\pgfmathsetmacro{\anguloB}{`r porcentajes[1] * 3.6`}

% Segundo sector: `r porcentajes[1] * 3.6`° a `r (porcentajes[1] + porcentajes[2]) * 3.6`°
\pgfmathsetmacro{\anguloC}{`r (porcentajes[1] + porcentajes[2]) * 3.6`}

% Tercer sector: `r (porcentajes[1] + porcentajes[2]) * 3.6`° a `r (porcentajes[1] + porcentajes[2] + porcentajes[3]) * 3.6`°
\pgfmathsetmacro{\anguloD}{`r (porcentajes[1] + porcentajes[2] + porcentajes[3]) * 3.6`}

% Cuarto sector: `r (porcentajes[1] + porcentajes[2] + porcentajes[3]) * 3.6`° a `r (porcentajes[1] + porcentajes[2] + porcentajes[3] + porcentajes[4]) * 3.6`°
\pgfmathsetmacro{\anguloE}{`r (porcentajes[1] + porcentajes[2] + porcentajes[3] + porcentajes[4]) * 3.6`}

% Quinto sector: `r (porcentajes[1] + porcentajes[2] + porcentajes[3] + porcentajes[4]) * 3.6`° a 360°
\pgfmathsetmacro{\anguloF}{360}

% Dibujar sectores
% Sector 1
\fill[`r colores[1]`] (centro) -- (\anguloA:\radio) arc (\anguloA:\anguloB:\radio) -- cycle;
\draw[black, thick] (centro) -- (\anguloA:\radio) arc (\anguloA:\anguloB:\radio) -- cycle;

% Sector 2  
\fill[`r colores[2]`] (centro) -- (\anguloB:\radio) arc (\anguloB:\anguloC:\radio) -- cycle;
\draw[black, thick] (centro) -- (\anguloB:\radio) arc (\anguloB:\anguloC:\radio) -- cycle;

% Sector 3
\fill[`r colores[3]`] (centro) -- (\anguloC:\radio) arc (\anguloC:\anguloD:\radio) -- cycle;
\draw[black, thick] (centro) -- (\anguloC:\radio) arc (\anguloC:\anguloD:\radio) -- cycle;

% Sector 4
\fill[`r colores[4]`] (centro) -- (\anguloD:\radio) arc (\anguloD:\anguloE:\radio) -- cycle;
\draw[black, thick] (centro) -- (\anguloD:\radio) arc (\anguloD:\anguloE:\radio) -- cycle;

% Sector 5
\fill[`r colores[5]`] (centro) -- (\anguloE:\radio) arc (\anguloE:\anguloF:\radio) -- cycle;
\draw[black, thick] (centro) -- (\anguloE:\radio) arc (\anguloE:\anguloF:\radio) -- cycle;

% Etiquetas de porcentajes (en el centro de cada sector)
\pgfmathsetmacro{\etiquetaA}{(\anguloA + \anguloB)/2}
\pgfmathsetmacro{\etiquetaB}{(\anguloB + \anguloC)/2}
\pgfmathsetmacro{\etiquetaC}{(\anguloC + \anguloD)/2}
\pgfmathsetmacro{\etiquetaD}{(\anguloD + \anguloE)/2}
\pgfmathsetmacro{\etiquetaE}{(\anguloE + \anguloF)/2}

% Posicionar etiquetas a 60% del radio
\pgfmathsetmacro{\radioEtiqueta}{0.6 * \radio}

\node at (\etiquetaA:\radioEtiqueta) {\small \textbf{`r porcentajes[1]`\%}};
\node at (\etiquetaB:\radioEtiqueta) {\small \textbf{`r porcentajes[2]`\%}};
\node at (\etiquetaC:\radioEtiqueta) {\small \textbf{`r porcentajes[3]`\%}};
\node at (\etiquetaD:\radioEtiqueta) {\small \textbf{`r porcentajes[4]`\%}};
\node at (\etiquetaE:\radioEtiqueta) {\small \textbf{`r porcentajes[5]`\%}};

% Leyenda (opcional, posicionada a la derecha)
\coordinate (leyenda) at (3.5, 1);

\fill[`r colores[1]`] (leyenda) rectangle ++(0.3, 0.2);
\node[right] at ($(leyenda) + (0.4, 0.1)$) {\small `r etiquetas[1]`};

\fill[`r colores[2]`] ($(leyenda) + (0, -0.4)$) rectangle ++(0.3, 0.2);
\node[right] at ($(leyenda) + (0.4, -0.3)$) {\small `r etiquetas[2]`};

\fill[`r colores[3]`] ($(leyenda) + (0, -0.8)$) rectangle ++(0.3, 0.2);
\node[right] at ($(leyenda) + (0.4, -0.7)$) {\small `r etiquetas[3]`};

\fill[`r colores[4]`] ($(leyenda) + (0, -1.2)$) rectangle ++(0.3, 0.2);
\node[right] at ($(leyenda) + (0.4, -1.1)$) {\small `r etiquetas[4]`};

\fill[`r colores[5]`] ($(leyenda) + (0, -1.6)$) rectangle ++(0.3, 0.2);
\node[right] at ($(leyenda) + (0.4, -1.5)$) {\small `r etiquetas[5]`};

% Título del gráfico
\node[above] at (0, 2.5) {\large \textbf{`r titulo_grafico`}};

\end{tikzpicture}