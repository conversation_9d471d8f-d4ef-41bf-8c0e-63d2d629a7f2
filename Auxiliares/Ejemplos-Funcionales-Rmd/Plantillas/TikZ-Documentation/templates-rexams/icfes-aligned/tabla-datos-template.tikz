% Template TikZ para Tablas de Datos - Basado en Archivos Exitosos
% Fuente: estadistica_diagramas_caja_interpretacion_representacion_Nivel2_v2.Rmd
% Competencia: Pensamiento Aleatorio - Interpretación y Representación
% Nivel: 1-2 (Lectura de datos en tablas)

% CONFIGURACIÓN R REQUERIDA:
% En el chunk R definir:
% encabezado1 <- "Variable 1"
% encabezado2 <- "Variable 2" 
% datos_col1 <- c(valor1, valor2, valor3)
% datos_col2 <- c(valor4, valor5, valor6)
% unidad <- "cm" # opcional

\begin{tikzpicture}
\node[inner sep=0pt] {
  \begin{tabular}{|c|c|}
    \hline
    \textbf{`r encabezado1`} & \textbf{`r encabezado2`} \\
    \hline
    `r datos_col1[1]` & `r datos_col2[1]` \\
    \hline
    `r datos_col1[2]` & `r datos_col2[2]` \\
    \hline
    `r datos_col1[3]` & `r datos_col2[3]` \\
    \hline
  \end{tabular}
};
\end{tikzpicture}

% VARIANTE PARA TABLAS DINÁMICAS (usando sprintf):
% tabla_tikz <- sprintf('
% \\begin{tikzpicture}
% \\node[inner sep=0pt] {
%   \\begin{tabular}{|l|c|}
%   \\hline
%   \\textbf{%s} & \\textbf{%s (%s)} \\\\ \\hline
%   %s & %s \\\\ \\hline
%   %s & %s \\\\ \\hline
%   %s & %s \\\\ \\hline
%   \\end{tabular}
% };
% \\end{tikzpicture}
% ', encabezado1, encabezado2, unidad, 
%    datos_col1[1], datos_col2[1],
%    datos_col1[2], datos_col2[2], 
%    datos_col1[3], datos_col2[3])

% VENTAJAS DE ESTE PATRÓN:
% - Excelente compatibilidad con todos los formatos R-exams
% - Integración directa con variables R
% - No requiere bibliotecas TikZ adicionales
% - Escalable para cualquier número de filas
% - Soporte para unidades y formateo

% CASOS DE USO ICFES:
% - Tablas de frecuencia (estadística)
% - Datos experimentales (ciencias)
% - Comparación de valores (matemáticas)
% - Presentación de resultados (todas las áreas)