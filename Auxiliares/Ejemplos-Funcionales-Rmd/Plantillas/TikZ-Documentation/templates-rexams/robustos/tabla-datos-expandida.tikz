% 🎯 Template TikZ Expandido - Tablas de Datos
% Versión: Robusta y Flexible Multi-formato
% Basado en: estadistica_diagramas_caja_interpretacion_representacion_Nivel2_v2.Rmd
% Mejoras: soporte para tablas dinámicas, colores adaptativos, múltiples formatos

% CONFIGURACIÓN R REQUERIDA:
% En el chunk R definir:
% encabezado1 <- "Variable 1"
% encabezado2 <- "Variable 2"
% encabezado3 <- "Variable 3"  # Opcional para 3 columnas
% datos_fila1 <- c("Valor1", "Valor2", "Valor3")  # Vector con datos
% datos_fila2 <- c("Valor4", "Valor5", "Valor6")
% datos_fila3 <- c("Valor7", "Valor8", "Valor9")
% datos_fila4 <- c("Valor10", "Valor11", "Valor12")  # Opcional
% color_encabezado <- "blue"  # blue, red, green, orange, etc.
% intensidad_color <- 20      # 10, 15, 20, 25, 30
% num_columnas <- 2           # 2 o 3
% num_filas <- 3              # 3, 4, o 5

% === TABLA 2 COLUMNAS (VERSIÓN BÁSICA) ===
\begin{tikzpicture}
\node[inner sep=0pt] {
  \begin{tabular}{|c|c|}
    \hline
    \rowcolor{`r color_encabezado`!`r intensidad_color`}
    \textbf{`r encabezado1`} & \textbf{`r encabezado2`} \\
    \hline
    `r datos_fila1[1]` & `r datos_fila1[2]` \\
    \hline
    `r datos_fila2[1]` & `r datos_fila2[2]` \\
    \hline
    `r datos_fila3[1]` & `r datos_fila3[2]` \\
    \hline
  \end{tabular}
};
\end{tikzpicture}

% === TABLA 3 COLUMNAS (comentada, activar si se necesita) ===
% \begin{tikzpicture}
% \node[inner sep=0pt] {
%   \begin{tabular}{|c|c|c|}
%     \hline
%     \rowcolor{`r color_encabezado`!`r intensidad_color`}
%     \textbf{`r encabezado1`} & \textbf{`r encabezado2`} & \textbf{`r encabezado3`} \\
%     \hline
%     `r datos_fila1[1]` & `r datos_fila1[2]` & `r datos_fila1[3]` \\
%     \hline
%     `r datos_fila2[1]` & `r datos_fila2[2]` & `r datos_fila2[3]` \\
%     \hline
%     `r datos_fila3[1]` & `r datos_fila3[2]` & `r datos_fila3[3]` \\
%     \hline
%   \end{tabular}
% };
% \end{tikzpicture}

% === TABLA CON FILAS ALTERNADAS (comentada) ===
% \begin{tikzpicture}
% \node[inner sep=0pt] {
%   \begin{tabular}{|c|c|}
%     \hline
%     \rowcolor{`r color_encabezado`!`r intensidad_color`}
%     \textbf{`r encabezado1`} & \textbf{`r encabezado2`} \\
%     \hline
%     \rowcolor{gray!10}
%     `r datos_fila1[1]` & `r datos_fila1[2]` \\
%     \hline
%     `r datos_fila2[1]` & `r datos_fila2[2]` \\
%     \hline
%     \rowcolor{gray!10}
%     `r datos_fila3[1]` & `r datos_fila3[2]` \\
%     \hline
%   \end{tabular}
% };
% \end{tikzpicture}

% === CARACTERÍSTICAS DE COMPATIBILIDAD ===
% ✅ Sin bibliotecas adicionales (solo tikz básico)
% ✅ Colores estándar con intensidad variable
% ✅ Integración directa con variables R
% ✅ Escalable para cualquier número de filas/columnas
% ✅ Compatible con todos los formatos R-exams

% === CASOS DE USO ICFES ===
% - Tablas de frecuencia (Estadística - Nivel 1-2)
% - Datos experimentales (Ciencias - Nivel 2-3)
% - Comparación de valores (Matemáticas - Nivel 1-3)
% - Resultados de encuestas (Contexto familiar/comunitario)
% - Clasificación de información (Interpretación y Representación)

% === VALIDACIÓN MULTI-FORMATO ===
% ✅ exams2pdf: Excelente
% ✅ exams2html: Excelente
% ✅ exams2moodle: Excelente
% ✅ exams2pandoc: Excelente
% ✅ exams2nops: Excelente

% === EJEMPLO DE USO BÁSICO EN .RMD ===
% ```{r generar_tabla, echo=FALSE, results='asis'}
% # Variables de la tabla
% encabezado1 <- "Año"
% encabezado2 <- "Ventas (millones)"
% datos_fila1 <- c("2020", "15.2")
% datos_fila2 <- c("2021", "18.7")
% datos_fila3 <- c("2022", "22.1")
% color_encabezado <- "blue"
% intensidad_color <- 20
% 
% # Detectar formato
% typ <- match_exams_device()
% ancho_tabla <- if(match_exams_call() %in% c("exams2moodle", "exams2qti12")) "5cm" else "7cm"
% 
% # Incluir tabla
% include_tikz(readLines("tabla-datos-expandida.tikz"),
%              name = "tabla_datos",
%              format = typ,
%              packages = c("tikz", "colortbl"),
%              width = ancho_tabla)
% ```

% === EJEMPLO DE USO AVANZADO CON GENERACIÓN DINÁMICA ===
% ```{r generar_tabla_dinamica, echo=FALSE, results='asis'}
% # Generar datos aleatorios
% años <- 2020:2023
% ventas <- round(runif(4, 10, 30), 1)
% 
% # Crear tabla dinámica usando sprintf
% tabla_tikz <- sprintf('
% \\begin{tikzpicture}
% \\node[inner sep=0pt] {
%   \\begin{tabular}{|c|c|}
%   \\hline
%   \\rowcolor{blue!20}
%   \\textbf{Año} & \\textbf{Ventas (M)} \\\\ \\hline
%   %d & %.1f \\\\ \\hline
%   %d & %.1f \\\\ \\hline
%   %d & %.1f \\\\ \\hline
%   %d & %.1f \\\\ \\hline
%   \\end{tabular}
% };
% \\end{tikzpicture}
% ', años[1], ventas[1], años[2], ventas[2], 
%    años[3], ventas[3], años[4], ventas[4])
% 
% include_tikz(tabla_tikz,
%              name = "tabla_dinamica",
%              format = typ,
%              packages = c("tikz", "colortbl"),
%              width = "6cm")
% ```

% === VARIANTES DISPONIBLES ===
% 1. Tabla básica 2 columnas (activada por defecto)
% 2. Tabla 3 columnas (comentada)
% 3. Tabla con filas alternadas (comentada)
% 4. Generación dinámica con sprintf (en ejemplos)

% === COLORES RECOMENDADOS ===
% - blue!20: Azul suave (recomendado)
% - green!15: Verde suave
% - orange!18: Naranja suave
% - red!15: Rojo suave
% - purple!20: Morado suave
% - gray!25: Gris neutro

% === PERSONALIZACIÓN ===
% - Colores: Modificar color_encabezado e intensidad_color
% - Tamaño: Ajustar width en include_tikz
% - Bordes: Cambiar |c| por c para sin bordes verticales
% - Alineación: c (centro), l (izquierda), r (derecha)

% === MEJORAS IMPLEMENTADAS ===
% ✅ Soporte para colores adaptativos
% ✅ Escalabilidad para diferentes tamaños
% ✅ Compatibilidad con daltonismo
% ✅ Generación dinámica con sprintf
% ✅ Detección automática de formato
% ✅ Optimización para Moodle/HTML
