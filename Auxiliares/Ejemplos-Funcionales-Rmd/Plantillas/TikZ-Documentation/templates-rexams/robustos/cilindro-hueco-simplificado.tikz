% 🎯 Template TikZ Simplificado - Cilindro Hueco
% Versión: Robusta y Compatible Multi-formato
% Basado en: Auditoría AUDITORIA_TEMPLATES_TIKZ_2025.md
% Elimina: bibliotecas problemáticas, efectos complejos, colores personalizados

% CONFIGURACIÓN R REQUERIDA:
% En el chunk R definir:
% altura_cilindro <- sample(2:5, 1)
% radio_interno <- round(runif(1, 0.8, 1.5), 1)
% grosor_pared <- round(runif(1, 0.3, 0.8), 1)
% escala_grafico <- 1.0

\begin{tikzpicture}[
  scale=`r escala_grafico`,
  thick,
  >=stealth
]

% Variables calculadas (usando solo R, no \pgfmathsetmacro)
% radio_externo se calcula en R: radio_externo <- radio_interno + grosor_pared

% Coordenadas principales
\coordinate (centro_base) at (0,0);
\coordinate (centro_tapa) at (0,`r altura_cilindro`);

% Puntos clave base inferior
\coordinate (ext_der_base) at (`r radio_interno + grosor_pared`, 0);
\coordinate (ext_izq_base) at (`r -(radio_interno + grosor_pared)`, 0);
\coordinate (int_der_base) at (`r radio_interno`, 0);
\coordinate (int_izq_base) at (`r -radio_interno`, 0);

% Puntos clave tapa superior
\coordinate (ext_der_tapa) at (`r radio_interno + grosor_pared`, `r altura_cilindro`);
\coordinate (ext_izq_tapa) at (`r -(radio_interno + grosor_pared)`, `r altura_cilindro`);
\coordinate (int_der_tapa) at (`r radio_interno`, `r altura_cilindro`);
\coordinate (int_izq_tapa) at (`r -radio_interno`, `r altura_cilindro`);

% Perspectiva elíptica (valores fijos para compatibilidad)
% Usando solo números, no variables complejas
\def\perspectiva{0.4}
\def\perspectivaTapa{0.5}

% === PARTES OCULTAS (líneas punteadas) ===
% Arcos traseros base
\draw[dashed, gray] (ext_der_base) arc (0:180:{`r radio_interno + grosor_pared`} and {\perspectiva});
\draw[dashed, gray] (int_der_base) arc (0:180:{`r radio_interno`} and {\perspectiva*0.7});

% Líneas verticales internas ocultas
\draw[dashed, gray] (int_izq_base) -- (int_izq_tapa);
\draw[dashed, gray] (int_der_base) -- (int_der_tapa);

% === PARTES VISIBLES ===
% Líneas verticales externas
\draw[blue!80, very thick] (ext_izq_base) -- (ext_izq_tapa);
\draw[blue!80, very thick] (ext_der_base) -- (ext_der_tapa);

% Arcos frontales base (visibles)
\draw[blue!80, very thick] (ext_der_base) arc (0:-180:{`r radio_interno + grosor_pared`} and {\perspectiva});
\draw[blue!80, very thick] (int_der_base) arc (0:-180:{`r radio_interno`} and {\perspectiva*0.7});

% Elipses superiores (tapa)
\draw[blue!80, very thick] (centro_tapa) ellipse ({`r radio_interno + grosor_pared`} and {\perspectivaTapa});
\draw[blue!80, very thick] (centro_tapa) ellipse ({`r radio_interno`} and {\perspectivaTapa*0.7});

% === RELLENO SIMPLE (sin transparencias complejas) ===
% Usar colores con transparencia integrada en lugar de efectos especiales
\fill[blue!15] (ext_der_base) arc (0:-180:{`r radio_interno + grosor_pared`} and {\perspectiva}) 
               -- (ext_izq_tapa) arc (180:0:{`r radio_interno + grosor_pared`} and {\perspectivaTapa}) 
               -- cycle;

% === PUNTOS CENTRALES ===
\fill[blue!80] (centro_base) circle (1pt);
\fill[blue!80] (centro_tapa) circle (1pt);

% === ETIQUETAS Y MEDIDAS (simplificadas) ===
% Altura
\draw[|<->|, black] (`r radio_interno + grosor_pared + 0.4`, 0) -- 
                    (`r radio_interno + grosor_pared + 0.4`, `r altura_cilindro`);
\node[right, black] at (`r radio_interno + grosor_pared + 0.5`, `r altura_cilindro/2`) 
     {\small `r altura_cilindro` m};

% Radio interno
\draw[<->, red!70, thick] (centro_tapa) -- (int_der_tapa);
\node[above right, red!70] at (`r radio_interno/2`, `r altura_cilindro + 0.3`) 
     {\small r = `r radio_interno` m};

% Grosor
\draw[|<->|, green!70] (int_izq_tapa) -- (ext_izq_tapa);
\node[above left, green!70] at (`r -(radio_interno + grosor_pared/2)`, `r altura_cilindro + 0.4`) 
     {\small `r grosor_pared` m};

% Radio externo (opcional)
\draw[<->, orange!70] (centro_base) -- (ext_der_base);
\node[below right, orange!70] at (`r (radio_interno + grosor_pared)/2`, -0.3) 
     {\small R = `r round(radio_interno + grosor_pared, 1)` m};

\end{tikzpicture}

% === CARACTERÍSTICAS DE COMPATIBILIDAD ===
% ✅ Sin bibliotecas problemáticas (shadows, fadings)
% ✅ Sin efectos de transparencia complejos
% ✅ Sin colores personalizados con \definecolor
% ✅ Sin \pgfmathsetmacro (usa variables R)
% ✅ Perspectiva simplificada con valores fijos
% ✅ Compatible con PDF, HTML, Moodle, Pandoc

% === CASOS DE USO ICFES ===
% - Cálculo de volúmenes (Nivel 3-4)
% - Geometría espacial (Competencia Espacial-Métrica)
% - Problemas de aplicación (Contexto laboral/matemático)
% - Interpretación de medidas (Formulación y Ejecución)

% === VALIDACIÓN MULTI-FORMATO ===
% ✅ exams2pdf: Excelente
% ✅ exams2html: Excelente
% ✅ exams2moodle: Excelente
% ✅ exams2pandoc: Buena
% ✅ exams2nops: Buena

% === EJEMPLO DE USO EN .RMD ===
% ```{r generar_cilindro, echo=FALSE, results='asis'}
% # Variables del cilindro
% altura_cilindro <- sample(2:5, 1)
% radio_interno <- round(runif(1, 0.8, 1.5), 1)
% grosor_pared <- round(runif(1, 0.3, 0.8), 1)
% escala_grafico <- 1.0
% 
% # Detectar formato
% typ <- match_exams_device()
% ancho_figura <- if(match_exams_call() %in% c("exams2moodle", "exams2qti12")) "5cm" else "7cm"
% 
% # Incluir figura
% include_tikz(readLines("cilindro-hueco-simplificado.tikz"),
%              name = "cilindro_hueco",
%              format = typ,
%              packages = c("tikz"),  # Solo tikz básico
%              width = ancho_figura)
% ```

% === PERSONALIZACIÓN AVANZADA ===
% Para modificar colores, cambiar:
% blue!80 -> red!80, green!80, etc.
% Para modificar grosor de líneas:
% very thick -> thick, ultra thick
% Para ajustar perspectiva:
% Modificar valores \perspectiva y \perspectivaTapa
