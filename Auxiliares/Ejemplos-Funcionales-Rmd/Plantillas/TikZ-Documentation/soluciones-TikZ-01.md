## Compatibilidad entre TikZ y r-exams: Aná<PERSON><PERSON>

### **Compatibilidad General**

**SÍ, la mayoría del código TikZ es compatible con r-exams**, pero existen **limitaciones específicas** que debes conocer. La función `include_tikz()` de r-exams está diseñada específicamente para facilitar la inclusión de gráficos TikZ en ejercicios.

### **Limitaciones Principales Identificadas**

#### 1. **Problemas con pgfplots**

- **Error común**: "Environment axis undefined" cuando se usa `\begin{axis}` y `\end{axis}`
- **Causa**: La librería `pgfplots` no se carga automáticamente en el entorno de compilación de r-exams
- **Solución**: Especificar explícitamente `library = "pgfplots"` en la función `include_tikz()`

#### 2. **Limitaciones de Librerías TikZ**

- **Librerías compatibles**: Las librerías básicas de TikZ funcionan bien
- **Librerías problemáticas**: 
  - `external` (tikzexternalize) - incompatible con el proceso de compilación de r-exams
  - Algunas librerías especializadas pueden requerir configuración adicional

#### 3. **Código TikZ Complejo**
- **Gráficos simples**: Perfectamente compatibles
- **Gráficos complejos**: Pueden requerir ajustes específicos en:
  - Configuración de librerías
  - Parámetros de compilación
  - Gestión de dependencias

### **Función `include_tikz()` - Capacidades y Limitaciones**

```r
include_tikz(tikz, name = "tikzpicture", format = NULL,
             library = NULL, width = NULL, markup = "tex", ...)
```

**Parámetros clave:**

- `library`: Especifica librerías TikZ necesarias (ej: `c("pgfplots", "arrows")`)
- `format`: Soporta PNG, SVG, PDF, TEX
- Compatible con salida HTML y LaTeX

### **Ejemplos de Uso Exitoso**

Según la documentación oficial de r-exams, los siguientes ejemplos funcionan correctamente:

1. **Autómatas** (usando librerías: `arrows`, `shapes.gates.logic.US`, `calc`)
2. **Compuertas lógicas** (usando librerías similares)
3. **Gráficos matemáticos básicos**

### **Código TikZ que NO funciona o requiere ajustes**

1. **Código con `tikzexternalize`**:
```latex
\usetikzlibrary{external}
\tikzexternalize
```

2. **Código pgfplots sin declarar librería**:
```latex
\begin{axis}
% Contenido del gráfico
\end{axis}
```

3. **Código con librerías complejas no declaradas**

### **Mejores Prácticas para Garantizar Compatibilidad**

#### 1. **Declarar Librerías Explícitamente**
```r
include_tikz(tikz_code, 
             library = c("pgfplots", "arrows", "shapes.gates.logic.US"))
```

#### 2. **Probar Primero con Código Simple**
```r
# Ejemplo básico que funciona
tikz_simple <- "
\\draw (0,0) -- (1,1);
\\node at (0.5,0.5) {Test};
"
include_tikz(tikz_simple)
```

#### 3. **Para pgfplots, usar formato específico**
```r
tikz_plot <- "
\\begin{axis}
\\addplot {x^2};
\\end{axis}
"
include_tikz(tikz_plot, library = "pgfplots")
```

### **Soluciones a Problemas Comunes**

#### Error "Environment axis undefined"
**Solución**: Agregar `library = "pgfplots"` en `include_tikz()`

#### Error de compilación con librerías
**Solución**: Especificar todas las librerías necesarias en el parámetro `library`

#### Problemas con tikzexternalize
**Solución**: Evitar usar `\tikzexternalize` - r-exams maneja la externalización automáticamente

### **Conclusión**

**No todo el código TikZ es directamente compatible con r-exams**, pero la **gran mayoría sí lo es** con las configuraciones apropiadas. Los principales problemas surgen con:

1. **pgfplots sin declarar librería**
2. **Librerías de externalización (tikzexternalize)**
3. **Código que depende de configuraciones específicas del preámbulo**

La función `include_tikz()` está bien diseñada y maneja la mayoría de casos de uso, pero requiere conocimiento de sus limitaciones y configuración apropiada para código complejo.

**Recomendación**: Para código TikZ complejo, siempre hacer pruebas incrementales, declarar explícitamente las librerías necesarias, y evitar características de externalización que entran en conflicto con el proceso de compilación de r-exams.