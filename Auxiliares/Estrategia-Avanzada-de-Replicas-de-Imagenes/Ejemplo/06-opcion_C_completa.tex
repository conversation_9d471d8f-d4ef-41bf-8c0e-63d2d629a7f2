\documentclass{article}
\usepackage[margin=0.25in]{geometry}
\usepackage{pgfplots}
\pgfplotsset{width=10cm,compat=1.9}

% We will externalize the figures
\usepgfplotslibrary{external}
\tikzexternalize

\begin{document}
	
	First example is 2D and 3D math expressions plotted side-by-side.
	
	%Here begins the 2D plot
	\begin{tikzpicture}
		\begin{axis}
			\addplot[color=red]{exp(x)};
		\end{axis}
	\end{tikzpicture}
	%Here ends the 2D plot
	\hskip 5pt
	%Here begins the 3D plot
	\begin{tikzpicture}
		\begin{axis}
			\addplot3[
			surf,
			]
			{exp(-x^2-y^2)*x};
		\end{axis}
	\end{tikzpicture}
	%Here ends the 3D plot
	
\end{document}