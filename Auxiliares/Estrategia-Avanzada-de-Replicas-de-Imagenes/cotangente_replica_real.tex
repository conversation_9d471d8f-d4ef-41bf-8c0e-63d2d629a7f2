\documentclass[border=2mm]{standalone}
\usepackage{tikz}
\usepackage{pgfplots}
\pgfplotsset{compat=1.18}

\begin{document}

% RÉPLICA REAL basada en OBSERVACIÓN CUIDADOSA de la imagen original
% Análisis: Función que va de MUY ALTO a MUY BAJO con discontinuidad dramática

\begin{tikzpicture}[scale=1.0]
\begin{axis}[
    xlabel={Ángulo $\alpha$},
    ylabel={Distancia PK},
    xmin=0, xmax=4.5,
    ymin=0, ymax=5,
    axis lines=left,
    arrow style={stealth},
    xlabel style={at={(axis description cs:1.02,0)}, anchor=west},
    ylabel style={at={(axis description cs:0,1.02)}, anchor=south, rotate=90},
    xtick=\empty,
    ytick=\empty,
    clip=false
]

% OBSERVACIÓN REAL: La curva empieza MUY ARRIBA y baja ABRUPTAMENTE
% Primera parte: Curva que baja dramáticamente desde arriba
\addplot[cyan, very thick, smooth, domain=0.1:1.4] {5*exp(-2*x) + 0.5};

% DISCONTINUIDAD: Salto dramático - la curva continúa MUCHO MÁS ABAJO
% Segunda parte: Curva que continúa cerca del eje X
\addplot[cyan, very thick, smooth, domain=1.8:4.3] {0.8*exp(-0.5*(x-1.8)) + 0.1};

% FLECHA VERTICAL CYAN (elemento distintivo de la imagen original)
\draw[cyan, very thick, -stealth] (axis cs:0.3,4.5) -- (axis cs:0.3,5.2);

% Líneas punteadas verticales EN LAS POSICIONES EXACTAS observadas
\draw[dashed, black, thick] (axis cs:1.2,0) -- (axis cs:1.2,5) 
    node[left, black, yshift=5pt] {\small $QP$};
\draw[dashed, black, thick] (axis cs:2.8,0) -- (axis cs:2.8,5) 
    node[left, black, yshift=5pt] {\small $I_1$};

% Punto destacado en la curva (observado en la imagen)
\addplot[cyan, mark=*, mark size=3pt, only marks] coordinates {(2.8,0.3)};

% Ejes con flechas (como en la imagen original)
\draw[black, thick, -stealth] (axis cs:0,0) -- (axis cs:4.7,0);
\draw[black, thick, -stealth] (axis cs:0,0) -- (axis cs:0,5.3);

\end{axis}
\end{tikzpicture}

\end{document}
