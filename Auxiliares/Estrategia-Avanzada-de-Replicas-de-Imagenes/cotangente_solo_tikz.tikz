% Código TikZ optimizado para Qtikz/Ktikz - Función Cotangente
% Generado por Sistema Inteligente de Templates
% Compatible con la imagen problemática que nos dio dificultades

\begin{tikzpicture}[scale=0.8]
\begin{axis}[
    xlabel={Ángulo $\alpha$},
    ylabel={Distancia PK},
    xmin=0, xmax=4.5,
    ymin=0, ymax=5,
    grid=major,
    axis lines=left,
    samples=100,
    domain=0.2:4.3,
    restrict y to domain=0:5
]

% Primera parte de la función cotangente (antes de la discontinuidad)
\addplot[cyan, very thick, smooth, domain=0.2:1.4] {1/tan(deg(x))};

% Segunda parte de la función cotangente (después de la discontinuidad)
\addplot[cyan, very thick, smooth, domain=1.7:3.0] {1/tan(deg(x))};

% Tercera parte de la función cotangente
\addplot[cyan, very thick, smooth, domain=3.3:4.3] {1/tan(deg(x))};

% Líneas punteadas verticales con etiquetas
\draw[dashed, black] (axis cs:1.5,0) -- (axis cs:1.5,5) node[above] {QP};
\draw[dashed, black] (axis cs:3.0,0) -- (axis cs:3.0,5) node[above] {$I_1$};

% Asíntotas verticales (aproximadas en π/2 y 3π/2)
\draw[gray, very thin] (axis cs:1.57,0) -- (axis cs:1.57,5);
\draw[gray, very thin] (axis cs:3.14,0) -- (axis cs:3.14,5);

% Puntos importantes en la curva
\addplot[cyan, mark=*, mark size=2pt, only marks] coordinates {
    (0.5,1.83) (1.0,1.56) (2.0,-1.84) (2.5,-0.75) (3.5,0.70) (4.0,1.16)
};

\end{axis}
\end{tikzpicture}
